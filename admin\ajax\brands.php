<?php
require_once '../lib/session.php';
require_once '../lib/functions.php';
require_once '../lib/db_connection.php';
AdminSession::requireLogin();

$action = $_POST['action'] ?? '';
$response = ['success' => false, 'message' => '', 'data' => null];

switch ($action) {
    case 'list':
        $brands = $db->dbFetchArray("SELECT id, name, logo, status, show_in_header, created_at, updated_at, is_deleted FROM tabl_brands WHERE is_deleted = 0 ORDER BY id DESC");
        foreach ($brands as &$brand) {
            if ($brand['logo']) {
                $brand['logo'] = '../' . ltrim($brand['logo'], '/');
            }
        }
        $response['success'] = true;
        $response['data'] = $brands;
        break;
    case 'get':
        $id = intval($_POST['id'] ?? 0);
        $brand = $db->dbFetchAssoc("SELECT * FROM tabl_brands WHERE id = ? AND is_deleted = 0", [$id]);
        if ($brand) {
            if ($brand['logo']) {
                $brand['logo'] = '../' . ltrim($brand['logo'], '/');
            }
            $response['success'] = true;
            $response['data'] = $brand;
        } else {
            $response['message'] = 'Brand not found.';
        }
        break;
    case 'save':
        $id = intval($_POST['id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $show_in_header = isset($_POST['show_in_header']) ? 1 : 0;
        $logo_path = null;
        if (!empty($_FILES['logo']['name'])) {
            $upload = Utils::uploadFile($_FILES['logo'], 'brands', ['jpg', 'jpeg', 'png', 'gif'], '../../assets/uploads/');
            if ($upload['success']) {
                $logo_path = $upload['relative_path'];
            } else {
                $response['message'] = $upload['message'];
                echo json_encode($response);
                exit;
            }
        }
        if ($id > 0) {
            // Edit
            $update = ['name' => $name, 'show_in_header' => $show_in_header];
            if ($logo_path) {
                $update['logo'] = $logo_path;
            } else {
                // If no new logo uploaded, keep the old logo
                $old = $db->dbFetchAssoc("SELECT logo FROM tabl_brands WHERE id = ?", [$id]);
                if ($old && $old['logo']) {
                    $update['logo'] = $old['logo'];
                }
            }
            $db->dbUpdate('tabl_brands', $update, 'id = ?', [$id]);
            $updated = $db->dbFetchAssoc("SELECT * FROM tabl_brands WHERE id = ?", [$id]);
            $response['data'] = $updated;
        } else {
            // Add
            $db->dbInsert('tabl_brands', [
                'name' => $name,
                'logo' => $logo_path,
                'show_in_header' => $show_in_header
            ]);
        }
        $response['success'] = true;
        break;
    case 'update_field':
        $id = intval($_POST['id'] ?? 0);
        $field = trim($_POST['field'] ?? '');
        $value = intval($_POST['value'] ?? 0);
        
        // Validate field name for security
        $allowed_fields = ['show_in_header'];
        if (!in_array($field, $allowed_fields)) {
            $response['message'] = 'Invalid field name.';
            echo json_encode($response);
            exit;
        }
        
        if ($id > 0) {
            $db->dbUpdate('tabl_brands', [$field => $value], 'id = ?', [$id]);
            $response['success'] = true;
        } else {
            $response['message'] = 'Invalid brand ID.';
        }
        break;
    case 'delete':
        $id = intval($_POST['id'] ?? 0);
        $db->dbUpdate('tabl_brands', ['is_deleted' => 1], 'id = ?', [$id]);
        $response['success'] = true;
        break;
    default:
        $response['message'] = 'Invalid action.';
}
header('Content-Type: application/json');
echo json_encode($response);
