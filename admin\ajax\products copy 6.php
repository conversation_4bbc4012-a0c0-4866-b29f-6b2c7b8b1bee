<?php
require_once '../lib/session.php';
AdminSession::requireLogin();
require_once '../lib/db_connection.php';
require_once '../lib/functions.php';

header('Content-Type: application/json');
$action = $_POST['action'] ?? '';

try {
    if ($action === 'list') {
        $rows = $db->dbFetchArray("
            SELECT p.*, b.name as brand_name, s.name as seller_name
            FROM tabl_products p
            LEFT JOIN tabl_brands b ON p.brand_id = b.id  
            LEFT JOIN tabl_sellers s ON p.seller_id = s.id
            WHERE p.deleted_at IS NULL
            ORDER BY p.created_at DESC
        ");

        // Fetch and attach multi-select values for each product
        foreach ($rows as &$product) {
            $product_id = $product['id'];
            // Categories
            $cats = $db->dbFetchArray("SELECT c.name FROM tabl_product_categories pc JOIN tabl_category c ON pc.category_id = c.id WHERE pc.product_id = ?", [$product_id]);
            $product['categories'] = array_column($cats, 'name');
            // First image
            $image = $db->dbFetchAssoc("SELECT image_path FROM tabl_product_images WHERE product_id = ? AND is_cover = 1 LIMIT 1", [$product_id]);
            $product['cover_image'] = $image ? $image['image_path'] : null;
        }

        // Get first image for each product
        foreach ($rows as &$product) {
            $image = $db->dbFetchAssoc("SELECT image_path FROM tabl_product_images WHERE product_id = {$product['id']} AND is_cover = 1 LIMIT 1");
            $product['cover_image'] = $image ? $image['image_path'] : null;
        }

        echo json_encode(['success' => true, 'data' => $rows]);
        exit;
    }

    if ($action === 'get') {
        $id = intval($_POST['id'] ?? 0);
        $product = $db->dbFetchAssoc("SELECT * FROM tabl_products WHERE id = $id AND deleted_at IS NULL");

        if ($product) {
            // Get variations
            $variations = $db->dbFetchArray("SELECT * FROM tabl_product_variations WHERE product_id = $id ORDER BY id");
            $product['variations'] = $variations;

            // Get images
            $images = $db->dbFetchArray("SELECT * FROM tabl_product_images WHERE product_id = $id ORDER BY is_cover DESC, id");
            $product['images'] = $images;

            echo json_encode(['success' => true, 'data' => $product]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Product not found']);
        }
        exit;
    }

    if ($action === 'save') {
        $id = intval($_POST['id'] ?? 0);
        $product_type = $_POST['product_type'] ?? 'simple';

        // Basic product data
        $title = trim($_POST['title'] ?? '');
        $parent_sku = trim($_POST['parent_sku'] ?? '');
        // Use first selected category from categories[] array for main category_id
        $category_id = 0;
        if (!empty($_POST['categories']) && is_array($_POST['categories'])) {
            $category_id = intval($_POST['categories'][0]);
        }
        $collection = trim($_POST['collection'] ?? 'None');

        // Validate collection value - ensure it's one of the allowed ENUM values
        $allowedCollections = ['None', 'Summer', 'Winter', 'Exclusive', 'Limited', 'Spring', 'Autumn', 'Premium'];
        if (!in_array($collection, $allowedCollections)) {
            $collection = 'None'; // Default to None if invalid
        }
        $brand_id = intval($_POST['brand_id'] ?? 0) ?: null;
        $seller_id = intval($_POST['seller_id'] ?? 0) ?: null;
        $unit_id = intval($_POST['unit_id'] ?? 0) ?: null;
        $manufacturing_by = trim($_POST['manufacturing_by'] ?? '');
        $marketing_by = trim($_POST['marketing_by'] ?? '');
        $chemical_name = trim($_POST['chemical_name'] ?? '');
        $gst_rate = floatval($_POST['gst_rate'] ?? 0);
        $hsn_code = trim($_POST['hsn_code'] ?? '');
        $short_description = trim($_POST['short_description'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $key_features = trim($_POST['key_features'] ?? '');
        $additional_info = trim($_POST['additional_info'] ?? '');
        $toxicity = trim($_POST['toxicity'] ?? '');
        $application = trim($_POST['application'] ?? '');
        $status = trim($_POST['status'] ?? 'active');

        if ($title === '' || $parent_sku === '' || $category_id == 0) {
            echo json_encode(['success' => false, 'message' => 'Title, Parent SKU and at least one Category are required']);
            exit;
        }

        // Start transaction
        $db->dbQuery("START TRANSACTION");

        if ($id > 0) {
            // Update existing product
            $db->dbQuery(
                "UPDATE tabl_products SET 
                product_type = ?, title = ?, parent_sku = ?, category_id = ?, collection = ?, 
                brand_id = ?, seller_id = ?, unit_id = ?, manufacturing_by = ?, 
                marketing_by = ?, chemical_name = ?, gst_rate = ?, hsn_code = ?, short_description = ?, 
                description = ?, key_features = ?, additional_info = ?, toxicity = ?, application = ?, 
                status = ?, updated_at = NOW() WHERE id = ? AND deleted_at = ?",
                [
                    $product_type,
                    $title,
                    $parent_sku,
                    $category_id,
                    $collection,
                    $brand_id,
                    $seller_id,
                    $unit_id,
                    $manufacturing_by,
                    $marketing_by,
                    $chemical_name,
                    $gst_rate,
                    $hsn_code,
                    $short_description,
                    $description,
                    $key_features,
                    $additional_info,
                    $toxicity,
                    $application,
                    $status,
                    $id,
                    null
                ]
            );

            $productId = $id;
        } else {
            // Insert new product - use dbInsert which returns the ID
            $insertData = [
                'product_type' => $product_type,
                'title' => $title,
                'parent_sku' => $parent_sku,
                'category_id' => $category_id,
                'collection' => $collection,
                'brand_id' => $brand_id,
                'seller_id' => $seller_id,
                'unit_id' => $unit_id,
                'manufacturing_by' => $manufacturing_by,
                'marketing_by' => $marketing_by,
                'chemical_name' => $chemical_name,
                'gst_rate' => $gst_rate,
                'hsn_code' => $hsn_code,
                'short_description' => $short_description,
                'description' => $description,
                'key_features' => $key_features,
                'additional_info' => $additional_info,
                'toxicity' => $toxicity,
                'application' => $application,
                'status' => $status,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $productId = $db->dbInsert('tabl_products', $insertData);

            if (!$productId) {
                throw new Exception('Failed to insert product');
            }
        }

        // Update junction tables for categories, crops, pests, diseases only if updating existing product
        if ($id > 0) {
            // Remove old entries
            $db->dbQuery("DELETE FROM tabl_product_categories WHERE product_id = ?", [$id]);
            $db->dbQuery("DELETE FROM tabl_product_crops WHERE product_id = ?", [$id]);
            $db->dbQuery("DELETE FROM tabl_product_pests WHERE product_id = ?", [$id]);
            $db->dbQuery("DELETE FROM tabl_product_diseases WHERE product_id = ?", [$id]);

            // Insert new entries
            if (!empty($_POST['categories']) && is_array($_POST['categories'])) {
                foreach ($_POST['categories'] as $cat_id) {
                    $db->dbQuery("INSERT INTO tabl_product_categories (product_id, category_id) VALUES (?, ?)", [$id, intval($cat_id)]);
                }
            }
            if (!empty($_POST['crops']) && is_array($_POST['crops'])) {
                foreach ($_POST['crops'] as $crop_id) {
                    $db->dbQuery("INSERT INTO tabl_product_crops (product_id, crop_id) VALUES (?, ?)", [$id, intval($crop_id)]);
                }
            }
            if (!empty($_POST['pests']) && is_array($_POST['pests'])) {
                foreach ($_POST['pests'] as $pest_id) {
                    $db->dbQuery("INSERT INTO tabl_product_pests (product_id, pest_id) VALUES (?, ?)", [$id, intval($pest_id)]);
                }
            }
            if (!empty($_POST['diseases']) && is_array($_POST['diseases'])) {
                foreach ($_POST['diseases'] as $disease_id) {
                    $db->dbQuery("INSERT INTO tabl_product_diseases (product_id, disease_id) VALUES (?, ?)", [$id, intval($disease_id)]);
                }
            }
        }

        // Handle variations
        if ($product_type === 'simple') {
            // For simple products, update or create the default variation
            $simple_sku = trim($_POST['simple_sku'] ?? $parent_sku);
            $simple_price = floatval($_POST['simple_price'] ?? 0);
            $simple_mrp_price = floatval($_POST['simple_mrp_price'] ?? 0);
            $simple_cost_price = floatval($_POST['simple_cost_price'] ?? 0);
            $simple_stock_quantity = intval($_POST['simple_stock_quantity'] ?? 0);
            $simple_low_stock_threshold = intval($_POST['simple_low_stock_threshold'] ?? 0);
            $simple_weight = floatval($_POST['simple_weight'] ?? 0) ?: null;
            $simple_dimensions = trim($_POST['simple_dimensions'] ?? '');
            $simple_is_featured = intval($_POST['simple_is_featured'] ?? 0);
            $simple_status = trim($_POST['simple_status'] ?? 'active');

            if ($id > 0) {
                // Update existing variation for simple product
                $existing_variation = $db->dbFetchAssoc("SELECT id FROM tabl_product_variations WHERE product_id = ? LIMIT 1", [$productId]);

                if ($existing_variation) {
                    // Update existing variation
                    $db->dbQuery(
                        "UPDATE tabl_product_variations SET 
                        name = ?, sku = ?, price = ?, mrp_price = ?, cost_price = ?, stock_quantity = ?, 
                        low_stock_threshold = ?, weight = ?, dimensions = ?, is_featured = ?, status = ?, 
                        updated_at = NOW() WHERE id = ?",
                        [
                            'Default',
                            $simple_sku,
                            $simple_price,
                            $simple_mrp_price,
                            $simple_cost_price,
                            $simple_stock_quantity,
                            $simple_low_stock_threshold,
                            $simple_weight,
                            $simple_dimensions,
                            $simple_is_featured,
                            $simple_status,
                            $existing_variation['id']
                        ]
                    );

                    $variation_id = $existing_variation['id'];
                } else {
                    // Create new variation if none exists
                    $variation_id = $db->dbInsert('tabl_product_variations', [
                        'product_id' => $productId,
                        'name' => 'Default',
                        'sku' => $simple_sku,
                        'price' => $simple_price,
                        'mrp_price' => $simple_mrp_price,
                        'cost_price' => $simple_cost_price,
                        'stock_quantity' => $simple_stock_quantity,
                        'low_stock_threshold' => $simple_low_stock_threshold,
                        'weight' => $simple_weight,
                        'dimensions' => $simple_dimensions,
                        'is_featured' => $simple_is_featured,
                        'status' => $simple_status,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);

                    // Generate variation code for new variation
                    if ($variation_id) {
                        $productCodeGenerator = new ProductCodeGenerator();
                        $variation_code = $productCodeGenerator->assignVariationCode($variation_id);
                        if (!$variation_code) {
                            error_log("Warning: Failed to generate variation code for variation ID: $variation_id");
                        }
                    }
                }
            } else {
                // Create new variation for new product
                $variation_id = $db->dbInsert('tabl_product_variations', [
                    'product_id' => $productId,
                    'name' => 'Default',
                    'sku' => $simple_sku,
                    'price' => $simple_price,
                    'mrp_price' => $simple_mrp_price,
                    'cost_price' => $simple_cost_price,
                    'stock_quantity' => $simple_stock_quantity,
                    'low_stock_threshold' => $simple_low_stock_threshold,
                    'weight' => $simple_weight,
                    'dimensions' => $simple_dimensions,
                    'is_featured' => $simple_is_featured,
                    'status' => $simple_status,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                // Generate variation code for the simple product variation
                if ($variation_id) {
                    $productCodeGenerator = new ProductCodeGenerator();
                    $variation_code = $productCodeGenerator->assignVariationCode($variation_id);
                    if (!$variation_code) {
                        error_log("Warning: Failed to generate variation code for variation ID: $variation_id");
                    }
                }
            }
        } else {
            // For variable products, update existing variations and create new ones
            $variations = $_POST['variations'] ?? [];

            if ($id > 0) {
                // Get existing variations
                $existing_variations = $db->dbFetchArray("SELECT * FROM tabl_product_variations WHERE product_id = ? ORDER BY id", [$productId]);
                $existing_variation_ids = array_column($existing_variations, 'id');
                $updated_variation_ids = [];

                foreach ($variations as $index => $variation) {
                    $var_id = intval($variation['id'] ?? 0);
                    $var_name = trim($variation['name'] ?? '');
                    $var_sku = trim($variation['sku'] ?? '');
                    $var_price = floatval($variation['price'] ?? 0);
                    $var_mrp_price = floatval($variation['mrp_price'] ?? 0);
                    $var_cost_price = floatval($variation['cost_price'] ?? 0);
                    $var_stock_quantity = intval($variation['stock_quantity'] ?? 0);
                    $var_weight = floatval($variation['weight'] ?? 0) ?: null;
                    $var_dimensions = trim($variation['dimensions'] ?? '');
                    $var_is_featured = intval($variation['is_featured'] ?? 0);
                    $var_status = trim($variation['status'] ?? 'active');

                    if ($var_name && $var_sku) {
                        if ($var_id > 0 && in_array($var_id, $existing_variation_ids)) {
                            // Update existing variation
                            $db->dbQuery(
                                "UPDATE tabl_product_variations SET 
                                name = ?, sku = ?, price = ?, mrp_price = ?, cost_price = ?, stock_quantity = ?, 
                                weight = ?, dimensions = ?, is_featured = ?, status = ?, updated_at = NOW() 
                                WHERE id = ? AND product_id = ?",
                                [
                                    $var_name,
                                    $var_sku,
                                    $var_price,
                                    $var_mrp_price,
                                    $var_cost_price,
                                    $var_stock_quantity,
                                    $var_weight,
                                    $var_dimensions,
                                    $var_is_featured,
                                    $var_status,
                                    $var_id,
                                    $productId
                                ]
                            );

                            $updated_variation_ids[] = $var_id;
                        } else {
                            // Create new variation
                            $variation_id = $db->dbInsert('tabl_product_variations', [
                                'product_id' => $productId,
                                'name' => $var_name,
                                'sku' => $var_sku,
                                'price' => $var_price,
                                'mrp_price' => $var_mrp_price,
                                'cost_price' => $var_cost_price,
                                'stock_quantity' => $var_stock_quantity,
                                'weight' => $var_weight,
                                'dimensions' => $var_dimensions,
                                'is_featured' => $var_is_featured,
                                'status' => $var_status,
                                'created_at' => date('Y-m-d H:i:s')
                            ]);

                            // Generate variation code for new variation
                            if ($variation_id) {
                                $productCodeGenerator = new ProductCodeGenerator();
                                $variation_code = $productCodeGenerator->assignVariationCode($variation_id);
                                if (!$variation_code) {
                                    error_log("Warning: Failed to generate variation code for variation ID: $variation_id");
                                }
                                $updated_variation_ids[] = $variation_id;
                            }
                        }
                    }
                }

                // Remove variations that are no longer in the form data
                $variations_to_delete = array_diff($existing_variation_ids, $updated_variation_ids);
                if (!empty($variations_to_delete)) {
                    $placeholders = str_repeat('?,', count($variations_to_delete) - 1) . '?';
                    $db->dbQuery("DELETE FROM tabl_product_variations WHERE id IN ($placeholders)", $variations_to_delete);
                }
            } else {
                // Create new variations for new product
                foreach ($variations as $variation) {
                    $var_name = trim($variation['name'] ?? '');
                    $var_sku = trim($variation['sku'] ?? '');
                    $var_price = floatval($variation['price'] ?? 0);
                    $var_mrp_price = floatval($variation['mrp_price'] ?? 0);
                    $var_cost_price = floatval($variation['cost_price'] ?? 0);
                    $var_stock_quantity = intval($variation['stock_quantity'] ?? 0);
                    $var_weight = floatval($variation['weight'] ?? 0) ?: null;
                    $var_dimensions = trim($variation['dimensions'] ?? '');
                    $var_is_featured = intval($variation['is_featured'] ?? 0);
                    $var_status = trim($variation['status'] ?? 'active');

                    if ($var_name && $var_sku) {
                        $variation_id = $db->dbInsert('tabl_product_variations', [
                            'product_id' => $productId,
                            'name' => $var_name,
                            'sku' => $var_sku,
                            'price' => $var_price,
                            'mrp_price' => $var_mrp_price,
                            'cost_price' => $var_cost_price,
                            'stock_quantity' => $var_stock_quantity,
                            'weight' => $var_weight,
                            'dimensions' => $var_dimensions,
                            'is_featured' => $var_is_featured,
                            'status' => $var_status,
                            'created_at' => date('Y-m-d H:i:s')
                        ]);

                        // Generate variation code for each variation
                        if ($variation_id) {
                            $productCodeGenerator = new ProductCodeGenerator();
                            $variation_code = $productCodeGenerator->assignVariationCode($variation_id);
                            if (!$variation_code) {
                                error_log("Warning: Failed to generate variation code for variation ID: $variation_id");
                            }
                        }
                    }
                }
            }
        }

        // Handle file uploads using the Utils::uploadFile function

        // Handle images from the table format
        if (isset($_FILES['product_images']) && is_array($_FILES['product_images']['name'])) {
            $imageFiles = $_FILES['product_images'];
            $imageAltTexts = $_POST['image_alt_text'] ?? [];
            $imageVariationIds = $_POST['image_variation_id'] ?? [];
            $coverImageIndex = intval($_POST['cover_image_index'] ?? -1);

            for ($i = 0; $i < count($imageFiles['name']); $i++) {
                if ($imageFiles['error'][$i] === UPLOAD_ERR_OK) {
                    // Create individual file array for Utils::uploadFile
                    $single_file = [
                        'name' => $imageFiles['name'][$i],
                        'type' => $imageFiles['type'][$i],
                        'tmp_name' => $imageFiles['tmp_name'][$i],
                        'error' => $imageFiles['error'][$i],
                        'size' => $imageFiles['size'][$i]
                    ];

                    $upload_result = Utils::uploadFile($single_file, 'products', ['jpg', 'jpeg', 'png', 'gif'], "../../assets/uploads/");

                    if ($upload_result['success']) {
                        $alt_text = trim($imageAltTexts[$i] ?? '') ?: $title . ' - Image ' . ($i + 1);
                        $is_cover = ($i === $coverImageIndex) ? 1 : 0;
                        $variation_id = !empty($imageVariationIds[$i]) ? intval($imageVariationIds[$i]) : null;

                        $db->dbQuery(
                            "INSERT INTO tabl_product_images (product_id, variation_id, image_path, alt_text, is_cover, created_at) VALUES (?, ?, ?, ?, ?, NOW())",
                            [$productId, $variation_id, $upload_result['filename'], $alt_text, $is_cover]
                        );
                    } else {
                        error_log("Product image upload failed: " . $upload_result['message']);
                    }
                }
            }
        }

        $db->dbQuery("COMMIT");
        echo json_encode(['success' => true, 'message' => 'Product saved successfully', 'id' => $productId]);
        exit;
    }

    if ($action === 'delete') {
        $id = intval($_POST['id'] ?? 0);
        if ($id > 0) {
            $db->dbQuery("UPDATE tabl_products SET deleted_at = NOW() WHERE id = ?", [$id]);
            echo json_encode(['success' => true, 'message' => 'Product deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Invalid product ID']);
        }
        exit;
    }

    if ($action === 'delete_image') {
        $image_id = intval($_POST['image_id'] ?? 0);
        if ($image_id <= 0) {
            echo json_encode(['success' => false, 'message' => 'Invalid image ID']);
            exit;
        }

        // Get image details before deleting
        $image = $db->dbFetchAssoc("SELECT image_path FROM tabl_product_images WHERE id = ?", [$image_id]);
        if (!$image) {
            echo json_encode(['success' => false, 'message' => 'Image not found']);
            exit;
        }

        // Delete image record from database
        $db->dbQuery("DELETE FROM tabl_product_images WHERE id = ?", [$image_id]);

        // Delete physical file
        $file_path = "../../assets/uploads/products/" . $image['image_path'];
        if (file_exists($file_path)) {
            unlink($file_path);
        }

        echo json_encode(['success' => true, 'message' => 'Image deleted successfully']);
        exit;
    }

    if ($action === 'update_image_variation') {
        $image_id = intval($_POST['image_id'] ?? 0);
        $variation_id = !empty($_POST['variation_id']) ? intval($_POST['variation_id']) : null;

        if ($image_id <= 0) {
            echo json_encode(['success' => false, 'message' => 'Invalid image ID']);
            exit;
        }

        // Update the image variation
        $db->dbQuery("UPDATE tabl_product_images SET variation_id = ? WHERE id = ?", [$variation_id, $image_id]);

        // Get variation name if variation_id is provided
        $variation_name = 'All Variations';
        if ($variation_id) {
            $variation = $db->dbFetchAssoc("SELECT name FROM tabl_product_variations WHERE id = ?", [$variation_id]);
            if ($variation) {
                $variation_name = $variation['name'];
            }
        }

        echo json_encode([
            'success' => true,
            'message' => 'Image variation updated successfully',
            'variation_name' => $variation_name
        ]);
        exit;
    }

    if ($action === 'generate_code') {
        $product_id = intval($_POST['product_id'] ?? 0);
        if ($product_id <= 0) {
            echo json_encode(['success' => false, 'message' => 'Invalid product ID']);
            exit;
        }

        // Check if product exists and doesn't already have a code
        $product = $db->dbFetchAssoc("SELECT id, product_code FROM tabl_products WHERE id = ? AND deleted_at IS NULL", [$product_id]);
        if (!$product) {
            echo json_encode(['success' => false, 'message' => 'Product not found']);
            exit;
        }

        if (!empty($product['product_code'])) {
            echo json_encode(['success' => false, 'message' => 'Product already has a code: ' . $product['product_code']]);
            exit;
        }

        // Generate product code
        $productCodeGenerator = new ProductCodeGenerator();
        $product_code = $productCodeGenerator->assignProductCode($product_id);

        if ($product_code) {
            echo json_encode(['success' => true, 'message' => 'Product code generated successfully', 'product_code' => $product_code]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to generate product code']);
        }
        exit;
    }

    if ($action === 'generate_variation_code') {
        $variation_id = intval($_POST['variation_id'] ?? 0);
        if ($variation_id <= 0) {
            echo json_encode(['success' => false, 'message' => 'Invalid variation ID']);
            exit;
        }

        // Check if variation exists and doesn't already have a code
        $variation = $db->dbFetchAssoc("SELECT id, variation_code FROM tabl_product_variations WHERE id = ?", [$variation_id]);
        if (!$variation) {
            echo json_encode(['success' => false, 'message' => 'Variation not found']);
            exit;
        }

        if (!empty($variation['variation_code'])) {
            echo json_encode(['success' => false, 'message' => 'Variation already has a code: ' . $variation['variation_code']]);
            exit;
        }

        // Generate variation code
        $productCodeGenerator = new ProductCodeGenerator();
        $variation_code = $productCodeGenerator->assignVariationCode($variation_id);

        if ($variation_code) {
            echo json_encode(['success' => true, 'message' => 'Variation code generated successfully', 'variation_code' => $variation_code]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to generate variation code']);
        }
        exit;
    }

    echo json_encode(['success' => false, 'message' => 'Invalid action']);
} catch (Exception $e) {
    $db->dbQuery("ROLLBACK");
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
