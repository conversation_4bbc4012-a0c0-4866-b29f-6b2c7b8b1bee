<?php
require_once '../lib/session.php';
AdminSession::requireLogin();
require_once '../lib/db_connection.php';

header('Content-Type: application/json');
$action = $_POST['action'] ?? '';

if ($action === 'list') {
    $rows = $db->dbFetchArray("SELECT u.*, DATE_FORMAT(u.created_at, '%Y-%m-%d') as created_at FROM tabl_users u WHERE u.is_deleted=0 ORDER BY u.id DESC");
    echo json_encode(['success' => true, 'data' => $rows]);
    exit;
}

if ($action === 'get') {
    $id = intval($_POST['id'] ?? 0);
    $row = $db->dbFetchAssoc("SELECT * FROM tabl_users WHERE id = $id AND is_deleted=0");
    if ($row) {
        echo json_encode(['success' => true, 'data' => $row]);
    } else {
        echo json_encode(['success' => false, 'message' => 'User not found']);
    }
    exit;
}

if ($action === 'save') {
    $id = intval($_POST['id'] ?? 0);
    $grant_id = intval($_POST['grant_id'] ?? 0);
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $status = trim($_POST['status'] ?? 'active');
    if ($name === '' || $grant_id == 0) {
        echo json_encode(['success' => false, 'message' => 'Name and Grant are required']);
        exit;
    }
    if ($id > 0) {
        $db->dbQuery("UPDATE tabl_users SET grant_id=?, name=?, email=?, phone=?, address=?, status=? WHERE id=? AND is_deleted=0", [$grant_id, $name, $email, $phone, $address, $status, $id]);
    } else {
        $db->dbQuery("INSERT INTO tabl_users (grant_id, name, email, phone, address, status) VALUES (?, ?, ?, ?, ?, ?)", [$grant_id, $name, $email, $phone, $address, $status]);
    }
    echo json_encode(['success' => true]);
    exit;
}

if ($action === 'delete') {
    $id = intval($_POST['id'] ?? 0);
    if ($id > 0) {
        $db->dbQuery("UPDATE tabl_users SET is_deleted=1 WHERE id=?", [$id]);
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
    }
    exit;
}

if ($action === 'change_password') {
    $id = intval($_POST['user_id'] ?? 0);
    $newPassword = trim($_POST['new_password'] ?? '');

    if ($id <= 0 || empty($newPassword)) {
        echo json_encode(['success' => false, 'message' => 'Invalid user ID or password']);
        exit;
    }

    if (strlen($newPassword) < 6) {
        echo json_encode(['success' => false, 'message' => 'Password must be at least 6 characters long']);
        exit;
    }

    // Hash the password using bcrypt
    $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT);

    $db->dbQuery("UPDATE tabl_users SET password=? WHERE id=? AND is_deleted=0", [$hashedPassword, $id]);
    echo json_encode(['success' => true]);
    exit;
}

if ($action === 'grants') {
    $rows = $db->dbFetchArray("SELECT id, name FROM tabl_grants WHERE is_deleted=0 AND status='active' ORDER BY name ASC");
    echo json_encode(['success' => true, 'data' => $rows]);
    exit;
}

echo json_encode(['success' => false, 'message' => 'Invalid action']);
