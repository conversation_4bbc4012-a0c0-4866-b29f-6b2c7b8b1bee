
<?php
require_once '../lib/session.php';
require_once '../lib/functions.php';
require_once '../lib/db_connection.php';
AdminSession::requireLogin();

// Function to generate URL-friendly slug
function generateSlug($text, $db, $id = 0) {
    // Basic slug generation
    $slug = strtolower(trim($text));
    $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    $slug = trim($slug, '-');
    
    if (empty($slug)) {
        $slug = 'category-' . time();
    }
    
    // Ensure uniqueness
    $originalSlug = $slug;
    $counter = 1;
    
    while (true) {
        $existingCategory = $db->dbFetchAssoc(
            "SELECT id FROM tabl_category WHERE slug = ? AND id != ? AND is_deleted = 0", 
            [$slug, $id]
        );
        
        if (!$existingCategory) {
            break;
        }
        
        $slug = $originalSlug . '-' . $counter;
        $counter++;
    }
    
    return $slug;
}

header('Content-Type: application/json');
$action = $_POST['action'] ?? '';
$response = ['success' => false, 'message' => '', 'data' => null];

switch ($action) {
    case 'list':
        $rows = $db->dbFetchArray("SELECT id, name, slug, description, image, status, is_deleted, parent_id, show_in_header, show_in_footer, show_in_trending, show_in_home FROM tabl_category WHERE is_deleted=0 ORDER BY id DESC");
        foreach ($rows as &$cat) {
            if ($cat['parent_id']) {
                $parent = $db->dbFetchAssoc("SELECT name FROM tabl_category WHERE id=?", [$cat['parent_id']]);
                $cat['parent_name'] = $parent ? $parent['name'] : '';
            } else {
                $cat['parent_name'] = '';
            }
            if ($cat['image']) {
                $cat['image'] = '../' . ltrim($cat['image'], '/');
            }
        }
        $response['success'] = true;
        $response['data'] = $rows;
        break;
    case 'get':
        $id = intval($_POST['id'] ?? 0);
        $row = $db->dbFetchAssoc("SELECT * FROM tabl_category WHERE id = ? AND is_deleted=0", [$id]);
        if ($row) {
            if ($row['image']) {
                $row['image'] = '../' . ltrim($row['image'], '/');
            }
            $response['success'] = true;
            $response['data'] = $row;
        } else {
            $response['message'] = 'Category not found.';
        }
        break;
    case 'save':
        $id = intval($_POST['id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $slug = trim($_POST['slug'] ?? '');
        $desc = trim($_POST['description'] ?? '');
        $status = trim($_POST['status'] ?? 'active');
        $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
        $show_in_header = isset($_POST['show_in_header']) ? 1 : 0;
        $show_in_footer = isset($_POST['show_in_footer']) ? 1 : 0;
        $show_in_trending = isset($_POST['show_in_trending']) ? 1 : 0;
        $show_in_home = isset($_POST['show_in_home']) ? 1 : 0;
        $image_path = null;
        if (!empty($_FILES['image']['name'])) {
            $upload = Utils::uploadFile($_FILES['image'], 'categories', ['jpg', 'jpeg', 'png', 'gif'], '../../assets/uploads/');
            if ($upload['success']) {
                $image_path = $upload['relative_path'];
            } else {
                $response['message'] = $upload['message'];
                echo json_encode($response);
                exit;
            }
        }
        if ($name === '') {
            $response['message'] = 'Name is required';
            echo json_encode($response);
            exit;
        }
        
        // Generate slug if not provided
        if (empty($slug)) {
            $slug = generateSlug($name, $db, $id);
        } else {
            $slug = generateSlug($slug, $db, $id);
        }
        if ($id > 0) {
            $update = [
                'name' => $name,
                'slug' => $slug,
                'description' => $desc,
                'status' => $status,
                'parent_id' => $parent_id,
                'show_in_header' => $show_in_header,
                'show_in_footer' => $show_in_footer,
                'show_in_trending' => $show_in_trending,
                'show_in_home' => $show_in_home
            ];
            if ($image_path) {
                $update['image'] = $image_path;
            } else {
                $old = $db->dbFetchAssoc("SELECT image FROM tabl_category WHERE id = ?", [$id]);
                if ($old && $old['image']) {
                    $update['image'] = $old['image'];
                }
            }
            $db->dbUpdate('tabl_category', $update, 'id = ?', [$id]);
            $updated = $db->dbFetchAssoc("SELECT * FROM tabl_category WHERE id = ?", [$id]);
            $response['data'] = $updated;
        } else {
            $db->dbInsert('tabl_category', [
                'name' => $name,
                'slug' => $slug,
                'description' => $desc,
                'status' => $status,
                'parent_id' => $parent_id,
                'image' => $image_path,
                'show_in_header' => $show_in_header,
                'show_in_footer' => $show_in_footer,
                'show_in_trending' => $show_in_trending,
                'show_in_home' => $show_in_home
            ]);
        }
        $response['success'] = true;
        break;
    case 'update_field':
        $id = intval($_POST['id'] ?? 0);
        $field = trim($_POST['field'] ?? '');
        $value = intval($_POST['value'] ?? 0);
        
        // Validate field name for security
        $allowed_fields = ['show_in_header', 'show_in_footer', 'show_in_trending', 'show_in_home'];
        if (!in_array($field, $allowed_fields)) {
            $response['message'] = 'Invalid field name.';
            echo json_encode($response);
            exit;
        }
        
        if ($id > 0) {
            $db->dbUpdate('tabl_category', [$field => $value], 'id = ?', [$id]);
            $response['success'] = true;
        } else {
            $response['message'] = 'Invalid category ID.';
        }
        break;
    case 'delete':
        $id = intval($_POST['id'] ?? 0);
        $db->dbUpdate('tabl_category', ['is_deleted' => 1], 'id = ?', [$id]);
        $response['success'] = true;
        break;
    default:
        $response['message'] = 'Invalid action.';
}
echo json_encode($response);
